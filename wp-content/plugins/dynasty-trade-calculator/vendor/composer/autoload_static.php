<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitd5f1945ef04d69fe8d53cf5ad5e70576
{
    public static $files = array (
        '6124b4c8570aa390c21fafd04a26c69f' => __DIR__ . '/..' . '/myclabs/deep-copy/src/DeepCopy/deep_copy.php',
        'ec07570ca5a812141189b1fa81503674' => __DIR__ . '/..' . '/phpunit/phpunit/src/Framework/Assert/Functions.php',
    );

    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'Php<PERSON>arser\\' => 10,
        ),
        'E' => 
        array (
            'ExtPHP\\XmlToJson\\' => 17,
        ),
        'D' => 
        array (
            'DynastyTradeCalculator\\' => 23,
            'Doctrine\\Instantiator\\' => 22,
            'DeepCopy\\' => 9,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PhpParser\\' => 
        array (
            0 => __DIR__ . '/..' . '/nikic/php-parser/lib/PhpParser',
        ),
        'ExtPHP\\XmlToJson\\' => 
        array (
            0 => __DIR__ . '/..' . '/extphp/xml-to-json/src',
        ),
        'DynastyTradeCalculator\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
        'Doctrine\\Instantiator\\' => 
        array (
            0 => __DIR__ . '/..' . '/doctrine/instantiator/src/Doctrine/Instantiator',
        ),
        'DeepCopy\\' => 
        array (
            0 => __DIR__ . '/..' . '/myclabs/deep-copy/src/DeepCopy',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitd5f1945ef04d69fe8d53cf5ad5e70576::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitd5f1945ef04d69fe8d53cf5ad5e70576::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitd5f1945ef04d69fe8d53cf5ad5e70576::$classMap;

        }, null, ClassLoader::class);
    }
}
